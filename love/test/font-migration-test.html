<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体迁移验证测试 - Love Project</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .font-test {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .font-name {
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 5px;
        }
        
        .font-sample {
            font-size: 24px;
            margin: 10px 0;
        }
        
        .status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            margin-left: 10px;
        }
        
        .status.success { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.loading { background: #ff9800; }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        /* 字体样式测试 */
        .courgette { font-family: 'Courgette', cursive; }
        .great-vibes { font-family: 'Great Vibes', cursive; }
        .dancing-script { font-family: 'Dancing Script', cursive; }
        .poppins { font-family: 'Poppins', sans-serif; }
        .inter { font-family: 'Inter', sans-serif; }
        .playfair { font-family: 'Playfair Display', serif; }
        .zi-xiaohun-gouyu { font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive; }
        .zi-xiaohun-sanfen { font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif; }
        .zi-hun-xingyun { font-family: 'ZiHunXingYunFeiBai', 'Dancing Script', cursive; }
    </style>
    
    <!-- 导入字体样式 -->
    <link rel="stylesheet" href="/src/client/styles/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 字体迁移验证测试</h1>
            <p>验证R2优先+本地降级的双层字体架构</p>
        </div>
        
        <div class="controls">
            <button onclick="startTest()">开始测试</button>
            <button onclick="testR2Only()">仅测试R2</button>
            <button onclick="testLocalOnly()">仅测试本地</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h2>📊 字体加载状态</h2>
            <div id="font-status"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 字体显示测试</h2>
            <div class="font-test">
                <div class="font-name">Courgette <span id="courgette-status" class="status loading">测试中</span></div>
                <div class="font-sample courgette">Love is in the air, everywhere I look around</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">Great Vibes <span id="great-vibes-status" class="status loading">测试中</span></div>
                <div class="font-sample great-vibes">Beautiful moments together</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">Dancing Script <span id="dancing-script-status" class="status loading">测试中</span></div>
                <div class="font-sample dancing-script">Dancing through life with you</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">Poppins <span id="poppins-status" class="status loading">测试中</span></div>
                <div class="font-sample poppins">Modern and clean typography</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">Inter <span id="inter-status" class="status loading">测试中</span></div>
                <div class="font-sample inter">Professional and readable text</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">Playfair Display <span id="playfair-status" class="status loading">测试中</span></div>
                <div class="font-sample playfair">Elegant serif typography</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">字小魂勾玉行书 <span id="zi-xiaohun-gouyu-status" class="status loading">测试中</span></div>
                <div class="font-sample zi-xiaohun-gouyu">爱情如诗如画，岁月静好</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">字小魂三分行楷 <span id="zi-xiaohun-sanfen-status" class="status loading">测试中</span></div>
                <div class="font-sample zi-xiaohun-sanfen">相遇是缘分，相守是幸福</div>
            </div>
            
            <div class="font-test">
                <div class="font-name">字魂行云飞白体 <span id="zi-hun-xingyun-status" class="status loading">测试中</span></div>
                <div class="font-sample zi-hun-xingyun">执子之手，与子偕老</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log">等待测试开始...</div>
        </div>
    </div>
    
    <!-- 加载FontController -->
    <script src="/src/client/assets/fonts/font-controller.js"></script>
    
    <script>
        let testLog = document.getElementById('test-log');
        let fontController;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLog.textContent += `[${timestamp}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            testLog.textContent = '';
        }
        
        function updateStatus(fontId, status, message = '') {
            const element = document.getElementById(fontId + '-status');
            if (element) {
                element.className = `status ${status}`;
                element.textContent = message || status;
            }
        }
        
        async function initFontController() {
            try {
                log('🚀 初始化FontController...');
                fontController = new FontController();
                await fontController.init();
                log('✅ FontController初始化成功');
                log(`📋 配置策略: ${fontController.config.strategy}`);
                log(`🔗 R2地址: ${fontController.config.urls.r2?.baseUrl || '未配置'}`);
                log(`💾 本地地址: ${fontController.config.urls.local?.baseUrl || '未配置'}`);
                return true;
            } catch (error) {
                log(`❌ FontController初始化失败: ${error.message}`);
                return false;
            }
        }
        
        async function testFont(fontFamily, elementId) {
            try {
                log(`🔤 测试字体: ${fontFamily}`);
                updateStatus(elementId, 'loading', '加载中');
                
                const result = await fontController.loadFont(fontFamily);
                
                if (result.success) {
                    updateStatus(elementId, 'success', `${result.source} 成功`);
                    log(`✅ ${fontFamily} 加载成功 (来源: ${result.source})`);
                } else {
                    updateStatus(elementId, 'error', '失败');
                    log(`❌ ${fontFamily} 加载失败`);
                }
                
                return result;
            } catch (error) {
                updateStatus(elementId, 'error', '错误');
                log(`❌ ${fontFamily} 测试出错: ${error.message}`);
                return { success: false, error: error.message };
            }
        }
        
        async function startTest() {
            log('🎯 开始完整字体迁移测试...');
            
            if (!await initFontController()) {
                return;
            }
            
            const fonts = [
                { family: 'Courgette', id: 'courgette' },
                { family: 'Great Vibes', id: 'great-vibes' },
                { family: 'Dancing Script', id: 'dancing-script' },
                { family: 'Poppins', id: 'poppins' },
                { family: 'Inter', id: 'inter' },
                { family: 'Playfair Display', id: 'playfair' },
                { family: 'ZiXiaoHunGouYu', id: 'zi-xiaohun-gouyu' },
                { family: 'ZiXiaoHunSanFen', id: 'zi-xiaohun-sanfen' },
                { family: 'ZiHunXingYunFeiBai', id: 'zi-hun-xingyun' }
            ];
            
            let successCount = 0;
            let r2Count = 0;
            let localCount = 0;
            
            for (const font of fonts) {
                const result = await testFont(font.family, font.id);
                if (result.success) {
                    successCount++;
                    if (result.source === 'R2') r2Count++;
                    if (result.source === 'Local') localCount++;
                }
                
                // 短暂延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('📊 测试完成统计:');
            log(`   总字体数: ${fonts.length}`);
            log(`   成功加载: ${successCount}`);
            log(`   R2来源: ${r2Count}`);
            log(`   本地来源: ${localCount}`);
            log(`   成功率: ${(successCount / fonts.length * 100).toFixed(1)}%`);
            
            if (successCount === fonts.length) {
                log('🎉 所有字体迁移验证通过！');
            } else {
                log('⚠️ 部分字体加载失败，请检查配置');
            }
        }
        
        async function testR2Only() {
            log('🌐 测试仅R2加载...');
            // 这里可以临时修改配置只使用R2
            log('⚠️ R2专项测试功能待实现');
        }
        
        async function testLocalOnly() {
            log('💾 测试仅本地加载...');
            // 这里可以临时修改配置只使用本地
            log('⚠️ 本地专项测试功能待实现');
        }
        
        // 页面加载完成后自动开始测试
        window.addEventListener('load', () => {
            log('📄 页面加载完成，准备开始字体测试');
            setTimeout(startTest, 1000);
        });
    </script>
</body>
</html>
