<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Project - 字体展示测试页面</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/src/client/assets/images/favicon.svg">
    <link rel="alternate icon" href="/src/client/assets/images/favicon.svg">
    <link rel="mask-icon" href="/src/client/assets/images/favicon.svg" color="#ff6b9d">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        /* 动态背景效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(10px); }
            75% { transform: translateX(-10px) translateY(20px); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FF69B4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px 25px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .font-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .font-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .font-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .font-card:hover::before {
            left: 100%;
        }

        .font-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .font-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .font-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #FFD700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .font-info {
            font-size: 0.9rem;
            opacity: 0.7;
            text-align: right;
        }

        .font-samples {
            space-y: 20px;
        }

        .sample-group {
            margin-bottom: 25px;
        }

        .sample-label {
            font-size: 0.9rem;
            color: #FFD700;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .sample-text {
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            border-left: 4px solid #FFD700;
            margin-bottom: 10px;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .sample-text:hover {
            background: rgba(0, 0, 0, 0.3);
            transform: translateX(5px);
        }

        .weight-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .weight-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 字体样式定义 */
        .courgette { font-family: 'Courgette', cursive; }
        .great-vibes { font-family: 'Great Vibes', cursive; }
        .dancing-script { font-family: 'Dancing Script', cursive; }
        .dancing-script-bold { font-family: 'Dancing Script', cursive; font-weight: 700; }
        .poppins-light { font-family: 'Poppins', sans-serif; font-weight: 300; }
        .poppins { font-family: 'Poppins', sans-serif; font-weight: 400; }
        .poppins-medium { font-family: 'Poppins', sans-serif; font-weight: 500; }
        .poppins-semibold { font-family: 'Poppins', sans-serif; font-weight: 600; }
        .poppins-bold { font-family: 'Poppins', sans-serif; font-weight: 700; }
        .inter-light { font-family: 'Inter', sans-serif; font-weight: 300; }
        .inter { font-family: 'Inter', sans-serif; font-weight: 400; }
        .inter-medium { font-family: 'Inter', sans-serif; font-weight: 500; }
        .inter-semibold { font-family: 'Inter', sans-serif; font-weight: 600; }
        .inter-bold { font-family: 'Inter', sans-serif; font-weight: 700; }
        .playfair { font-family: 'Playfair Display', serif; font-weight: 400; }
        .playfair-medium { font-family: 'Playfair Display', serif; font-weight: 500; }
        .playfair-semibold { font-family: 'Playfair Display', serif; font-weight: 600; }
        .playfair-bold { font-family: 'Playfair Display', serif; font-weight: 700; }
        .zi-xiaohun-gouyu { font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive; }
        .zi-xiaohun-sanfen { font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif; }
        .zi-hun-xingyun { font-family: 'ZiHunXingYunFeiBai', 'Dancing Script', cursive; }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .font-grid { grid-template-columns: 1fr; }
            .stats { gap: 15px; }
            .stat-item { padding: 10px 15px; }
            .font-card { padding: 20px; }
            .weight-demo { flex-direction: column; }
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }

        .footer p {
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .tech-badge {
            display: inline-block;
            background: rgba(255, 215, 0, 0.2);
            color: #FFD700;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 0 5px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }
    </style>
    
    <!-- 导入字体样式 -->
    <link rel="stylesheet" href="/src/client/styles/style.css">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1>🎨 Love Project 字体展示</h1>
            <p>双层CDN架构 · R2优先+本地降级 · 完美字体体验</p>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">21</div>
                    <div class="stat-label">字体文件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">9</div>
                    <div class="stat-label">字体族</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">可用性</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">woff2</div>
                    <div class="stat-label">优化格式</div>
                </div>
            </div>
        </div>

        <!-- 字体展示网格 -->
        <div class="font-grid">
            <!-- Courgette 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Courgette</div>
                    <div class="font-info">35.46 KB<br>装饰字体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text courgette" style="font-size: 28px;">
                            Love is in the air, everywhere I look around
                        </div>
                        <div class="sample-text courgette" style="font-size: 20px;">
                            Beautiful moments together, creating memories that last forever
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text courgette" style="font-size: 24px;">
                            爱情如诗如画，岁月静好如初
                        </div>
                    </div>
                </div>
            </div>

            <!-- Great Vibes 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Great Vibes</div>
                    <div class="font-info">154.57 KB<br>手写字体</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text great-vibes" style="font-size: 32px;">
                            Dancing through life with you
                        </div>
                        <div class="sample-text great-vibes" style="font-size: 24px;">
                            Every moment is a treasure when we're together
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text great-vibes" style="font-size: 28px;">
                            执子之手，与子偕老
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dancing Script 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Dancing Script</div>
                    <div class="font-info">74.55 KB + 74.8 KB<br>多字重手写</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text dancing-script" style="font-size: 28px;">
                            Regular: Love grows more beautiful with time
                        </div>
                        <div class="sample-text dancing-script-bold" style="font-size: 28px;">
                            Bold: Forever and always, my heart belongs to you
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text dancing-script" style="font-size: 24px;">
                            相遇是缘分，相守是幸福
                        </div>
                    </div>
                    <div class="weight-demo">
                        <div class="weight-item dancing-script">Regular (400)</div>
                        <div class="weight-item dancing-script-bold">Bold (700)</div>
                    </div>
                </div>
            </div>

            <!-- Poppins 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Poppins</div>
                    <div class="font-info">~150 KB × 5<br>现代无衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text poppins" style="font-size: 24px;">
                            Modern and clean typography for digital experiences
                        </div>
                        <div class="sample-text poppins-medium" style="font-size: 20px;">
                            Professional design meets beautiful aesthetics
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text poppins" style="font-size: 20px;">
                            现代简洁的设计理念，完美的用户体验
                        </div>
                    </div>
                    <div class="weight-demo">
                        <div class="weight-item poppins-light">Light (300)</div>
                        <div class="weight-item poppins">Regular (400)</div>
                        <div class="weight-item poppins-medium">Medium (500)</div>
                        <div class="weight-item poppins-semibold">SemiBold (600)</div>
                        <div class="weight-item poppins-bold">Bold (700)</div>
                    </div>
                </div>
            </div>

            <!-- Inter 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Inter</div>
                    <div class="font-info">~318 KB × 5<br>专业可读</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text inter" style="font-size: 22px;">
                            Designed for excellent readability and user interfaces
                        </div>
                        <div class="sample-text inter-medium" style="font-size: 18px;">
                            Perfect balance between functionality and beauty
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text inter" style="font-size: 18px;">
                            专为用户界面设计的高可读性字体
                        </div>
                    </div>
                    <div class="weight-demo">
                        <div class="weight-item inter-light">Light (300)</div>
                        <div class="weight-item inter">Regular (400)</div>
                        <div class="weight-item inter-medium">Medium (500)</div>
                        <div class="weight-item inter-semibold">SemiBold (600)</div>
                        <div class="weight-item inter-bold">Bold (700)</div>
                    </div>
                </div>
            </div>

            <!-- Playfair Display 字体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">Playfair Display</div>
                    <div class="font-info">~120 KB × 4<br>优雅衬线</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text playfair" style="font-size: 26px;">
                            Elegant serif typography for sophisticated design
                        </div>
                        <div class="sample-text playfair-medium" style="font-size: 22px;">
                            Classic beauty meets contemporary functionality
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text playfair" style="font-size: 20px;">
                            优雅的衬线字体，彰显经典与现代的完美融合
                        </div>
                    </div>
                    <div class="weight-demo">
                        <div class="weight-item playfair">Regular (400)</div>
                        <div class="weight-item playfair-medium">Medium (500)</div>
                        <div class="weight-item playfair-semibold">SemiBold (600)</div>
                        <div class="weight-item playfair-bold">Bold (700)</div>
                    </div>
                </div>
            </div>

            <!-- 字小魂勾玉行书 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">字小魂勾玉行书</div>
                    <div class="font-info">5.5 MB<br>中文行书</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text zi-xiaohun-gouyu" style="font-size: 32px;">
                            爱情如诗如画，岁月静好如初
                        </div>
                        <div class="sample-text zi-xiaohun-gouyu" style="font-size: 28px;">
                            山有木兮木有枝，心悦君兮君不知
                        </div>
                        <div class="sample-text zi-xiaohun-gouyu" style="font-size: 24px;">
                            愿得一心人，白首不相离
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text zi-xiaohun-gouyu" style="font-size: 24px;">
                            Love is the poetry of the senses
                        </div>
                    </div>
                </div>
            </div>

            <!-- 字小魂三分行楷 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">字小魂三分行楷</div>
                    <div class="font-info">8.88 MB<br>中文行楷</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text zi-xiaohun-sanfen" style="font-size: 30px;">
                            相遇是缘分，相守是幸福
                        </div>
                        <div class="sample-text zi-xiaohun-sanfen" style="font-size: 26px;">
                            时光荏苒，唯有真情永恒不变
                        </div>
                        <div class="sample-text zi-xiaohun-sanfen" style="font-size: 22px;">
                            执子之手，与子偕老，此生无憾
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text zi-xiaohun-sanfen" style="font-size: 22px;">
                            Together we create beautiful memories
                        </div>
                    </div>
                </div>
            </div>

            <!-- 字魂行云飞白体 -->
            <div class="font-card fade-in">
                <div class="font-header">
                    <div class="font-name">字魂行云飞白体</div>
                    <div class="font-info">5.25 MB<br>中文艺术</div>
                </div>
                <div class="font-samples">
                    <div class="sample-group">
                        <div class="sample-label">中文展示 (Chinese)</div>
                        <div class="sample-text zi-hun-xingyun" style="font-size: 34px;">
                            执子之手，与子偕老
                        </div>
                        <div class="sample-text zi-hun-xingyun" style="font-size: 28px;">
                            云卷云舒，花开花落，唯爱永恒
                        </div>
                        <div class="sample-text zi-hun-xingyun" style="font-size: 24px;">
                            岁月如歌，爱情如诗，美好如梦
                        </div>
                    </div>
                    <div class="sample-group">
                        <div class="sample-label">英文展示 (English)</div>
                        <div class="sample-text zi-hun-xingyun" style="font-size: 24px;">
                            Forever and always, my love
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="footer fade-in">
            <p>🎨 Love Project 字体系统 - 双层CDN架构</p>
            <p>
                <span class="tech-badge">R2 CDN</span>
                <span class="tech-badge">本地降级</span>
                <span class="tech-badge">woff2优化</span>
                <span class="tech-badge">智能加载</span>
            </p>
            <p style="margin-top: 15px; font-size: 0.9rem; opacity: 0.7;">
                所有字体均已优化为woff2格式，通过双层CDN架构确保最佳加载体验
            </p>
        </div>
    </div>

    <script>
        // 添加加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.font-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 150);
            });
        });

        // 添加鼠标跟随效果
        document.addEventListener('mousemove', function(e) {
            const cards = document.querySelectorAll('.font-card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    const rotateX = (y - centerY) / 20;
                    const rotateY = (centerX - x) / 20;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-5px)`;
                } else {
                    card.style.transform = '';
                }
            });
        });
    </script>
</body>
</html>
