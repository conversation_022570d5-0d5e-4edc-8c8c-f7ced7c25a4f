<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff8a80;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffd54f;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#heartGradient)" opacity="0.1"/>
  
  <!-- 爱心图标 -->
  <path d="M16 28c-1.2 0-2.4-0.4-3.2-1.2L6.4 20.4c-3.2-3.2-3.2-8.4 0-11.6 1.6-1.6 3.6-2.4 5.8-2.4s4.2 0.8 5.8 2.4l1.6 1.6c0.4 0.4 1.2 0.4 1.6 0l1.6-1.6c1.6-1.6 3.6-2.4 5.8-2.4s4.2 0.8 5.8 2.4c3.2 3.2 3.2 8.4 0 11.6l-6.4 6.4C18.4 27.6 17.2 28 16 28z" 
        fill="url(#heartGradient)" 
        filter="url(#glow)"
        transform="scale(0.7) translate(6.8, 6.8)"/>
  
  <!-- 小星星装饰 -->
  <circle cx="8" cy="8" r="1" fill="#ffd54f" opacity="0.8"/>
  <circle cx="24" cy="10" r="0.8" fill="#ff8a80" opacity="0.6"/>
  <circle cx="26" cy="24" r="1.2" fill="#ff6b9d" opacity="0.7"/>
  <circle cx="6" cy="26" r="0.6" fill="#ffd54f" opacity="0.5"/>
</svg>
